<!DOCTYPE html>
<html>

<head>
	<title>Admin Panel</title>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="shortcut icon" href="{{ url_for('views.themes', theme='core', path='img/favicon.ico') }}" type="image/x-icon">
	<link rel="icon" href="{{ url_for('views.themes', theme='core', path='img/favicon.ico') }}" type="image/x-icon">
	<link rel="stylesheet" href="{{ url_for('views.themes', theme='core', path='css/fonts.css') }}">
	<link rel="stylesheet" href="{{ url_for('views.themes', theme='core', path='css/main.css') }}">
	<link rel="stylesheet" href="{{ url_for('views.themes', theme='admin', path='css/admin.css') }}">
	<link rel="stylesheet" href="{{ url_for('views.themes', theme='admin', path='css/codemirror.css') }}">
	<script type="text/javascript">
		var init = {
			'urlRoot': "{{ request.script_root }}",
			'csrfNonce': "{{ Session.nonce }}",
			'userMode': "{{ get_config('user_mode') }}",
			'userId': {{ id if (id is defined) else 0 }},
			'start': {{ get_config("start") | tojson }},
			'end': {{ get_config("end") | tojson }},
		}
	</script>
	{% block stylesheets %} {% endblock %}
	{% for stylesheet in get_registered_admin_stylesheets() %}
		{% if stylesheet.startswith('http') %}
			<link rel="stylesheet" type="text/css" href="{{ stylesheet }}">
		{% elif request.script_root %}
			<link rel="stylesheet" type="text/css" href="{{ request.script_root }}/{{ stylesheet }}">
		{% else %}
			<link rel="stylesheet" type="text/css" href="{{ stylesheet }}">
		{% endif %}
	{% endfor %}
</head>

<body>
	<nav class="navbar navbar-expand-md navbar-dark bg-primary fixed-top">
		<div class="container">
			<a href="{{ url_for('views.static_html', route='/') }}" class="navbar-brand">CTFd</a>
			<button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#base-navbars"
					aria-controls="base-navbars" aria-expanded="false" aria-label="Toggle navigation">
				<span class="navbar-toggler-icon"></span>
			</button>
			<div class="collapse navbar-collapse" id="base-navbars">
				<ul class="navbar-nav mr-auto">
					<li class="nav-item"><a class="nav-link" href="{{ url_for('admin.statistics') }}">Statistics</a></li>
					<li class="nav-item"><a class="nav-link" href="{{ url_for('admin.notifications') }}">Notifications</a>
					</li>
					<li class="nav-item dropdown">
						<a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="true">Pages</a>
						<div class="dropdown-menu">
							<a class="dropdown-item" href="{{ url_for('admin.pages_listing') }}">All Pages</a>
							<a class="dropdown-item" href="{{ url_for('admin.pages_new') }}">New Page</a>
						</div>
					</li>
					<li class="nav-item"><a class="nav-link" href="{{ url_for('admin.users_listing') }}">Users</a></li>
					{% if get_config('user_mode') == 'teams' %}
					<li class="nav-item"><a class="nav-link" href="{{ url_for('admin.teams_listing') }}">Teams</a></li>
					{% endif %}
					<li class="nav-item"><a class="nav-link" href="{{ url_for('admin.scoreboard_listing') }}">Scoreboard</a></li>
					<li class="nav-item"><a class="nav-link" href="{{ url_for('admin.challenges_listing') }}">Challenges</a></li>
					<li class="nav-item dropdown">
						<a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown" role="button"
						   aria-haspopup="true" aria-expanded="true">Submissions</a>
						<div class="dropdown-menu">
							<a class="dropdown-item" href="{{ url_for('admin.submissions_listing') }}">All Submissions</a>
							<a class="dropdown-item" href="{{ url_for('admin.submissions_listing', submission_type='correct') }}">Correct Submissions</a>
							<a class="dropdown-item" href="{{ url_for('admin.submissions_listing', submission_type='incorrect') }}">Incorrect Submissions</a>
						</div>
					</li>
					<li class="nav-item"><a class="nav-link" href="{{ url_for('admin.config') }}">Config</a></li>

					{% set plugin_menu = get_admin_plugin_menu_bar() %}
					{% set plugins = get_configurable_plugins() %}
					{% if plugin_menu or plugins %}
						<li class="nav-item">
							<a class="nav-link d-none d-md-block d-lg-block">|</a>
						</li>

						{% for menu in plugin_menu %}
							{% if request.script_root %}
								{% set route = '/' + request.script_root + '/' + menu.route %}
							{% else %}
								{% set route = menu.route %}
							{% endif %}
							<li class="nav-item"><a class="nav-link" href="{{ route }}">{{ menu.title }}</a></li>
						{% endfor %}

						{% if plugins %}
						<li class="nav-item dropdown">
							<a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">Plugins <span class="caret"></span></a>
							<ul class="dropdown-menu">
									{% for plugin in plugins %}
											<a class="dropdown-item" href="{{ request.script_root }}{{ plugin.route }}">{{ plugin.name }}</a>
									{% endfor %}
							</ul>
						</li>
						{% endif %}
					{% endif %}
				</ul>
				<ul class="nav navbar-nav navbar-right">
					<li class="nav-item">
						<a class="nav-link" href="https://docs.ctfd.io/" target="_blank">
							<i class="far fa-question-circle"></i>
						</a>
					</li>
				</ul>
			</div>
		</div>
	</nav>

	<main role="main">
		{% block content %}
		{% endblock %}
	</main>

	<footer class="footer pt-2">
		<div class="container text-center">
			<a href="https://ctfd.io" class="text-secondary">
				<small class="text-muted">
					Powered by CTFd
				</small>
			</a>
			<span>
				<small class="text-muted"><br> Version {{ get_config('ctf_version') }}</small>
			</span>
		</div>
	</footer>

	<script defer src="{{ url_for('views.themes', theme='admin', path='js/vendor.bundle.js') }}"></script>
	<script defer src="{{ url_for('views.themes', theme='admin', path='js/core.js') }}"></script>
	<script defer src="{{ url_for('views.themes', theme='admin', path='js/helpers.js') }}"></script>
	<script defer src="{{ url_for('views.themes', theme='admin', path='js/components.js') }}"></script>

	{% block entrypoint %}
	<script defer src="{{ url_for('views.themes', theme='admin', path='js/pages/main.js') }}"></script>
	{% endblock %}

	{% block scripts %} {% endblock %}

	{% for script in get_registered_admin_scripts() %}
		{% if script.startswith('http') %}
			<script defer src="{{ script }}"></script>
		{% elif request.script_root %}
			<script defer src="{{ request.script_root }}/{{ script }}"></script>
		{% else %}
			<script defer src="{{ script }}"></script>
		{% endif %}
	{% endfor %}
</body>

</html>
