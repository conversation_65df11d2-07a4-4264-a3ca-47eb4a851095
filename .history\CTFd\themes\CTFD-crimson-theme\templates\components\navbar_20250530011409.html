<nav class="navbar navbar-expand-md navbar-dark bg-primary fixed-top">
    <div class="container">
        <a href="{{ url_for('views.static_html', route='/') }}" class="navbar-brand">
            {% if Configs.ctf_logo %}
                <img class="img-responsive ctf_logo" src="{{ url_for('views.files', path=Configs.ctf_logo) }}" height="25" alt="{{ Configs.ctf_name }}">
            {% else %}
                {{ Configs.ctf_name }}
            {% endif %}
        </a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#base-navbars"
                aria-controls="base-navbars" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="base-navbars">
            <ul class="navbar-nav mr-auto">
                {% for page in Plugins.user_menu_pages %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ page.route }}">{{ page.title }}</a>
                    </li>
                {% endfor %}

                {% if Configs.account_visibility != 'admins' %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('users.listing') }}">Users</a>
                    </li>
                    {% if Configs.user_mode == 'teams' %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('teams.listing') }}">Teams</a>
                    </li>
                    {% endif %}
                {% endif %}

                {% if Configs.score_visibility != 'admins' %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('scoreboard.listing') }}">Scoreboard</a>
                    </li>
                {% endif %}

                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('challenges.listing') }}">Challenges</a>
                </li>
            </ul>

            <hr class="d-sm-flex d-md-flex d-lg-none">

            <ul class="navbar-nav ml-md-auto d-block d-sm-flex d-md-flex">
                {% if authed() %}
                    {% if is_admin() %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('admin.view') }}">
                                <span class="d-block" data-toggle="tooltip" data-placement="bottom" title="Admin Panel">
                                    <i class="fas fa-wrench d-none d-md-block d-lg-none"></i>
                                </span>
                                <span class="d-sm-block d-md-none d-lg-block">
                                    <i class="fas fa-wrench pr-1"></i>Admin Panel
                                </span>
                            </a>
                        </li>
                    {% endif %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('views.notifications') }}">
                            <span class="d-block" data-toggle="tooltip" data-placement="bottom" title="Notifications">
                                <i class="fas fa-bell d-none d-md-block d-lg-none"></i>
                            </span>
                            <span class="d-sm-block d-md-none d-lg-block">
                                <i class="fas fa-bell pr-1"></i>
                                <span class="badge badge-pill badge-danger badge-notification"></span>
                                Notifications
                            </span>
                        </a>
                    </li>
                    {% if Configs.user_mode == "teams" %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('teams.private') }}">
                            <span class="d-block" data-toggle="tooltip" data-placement="bottom" title="Team">
                                <i class="fas fa-users d-none d-md-block d-lg-none"></i>
                            </span>
                            <span class="d-sm-block d-md-none d-lg-block">
                                <i class="fas fa-users pr-1"></i>Team
                            </span>
                        </a>
                    </li>
                    {% endif %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('users.private') }}">
                            <span class="d-block" data-toggle="tooltip" data-placement="bottom" title="Profile">
                                <i class="fas fa-user-circle d-none d-md-block d-lg-none"></i>
                            </span>
                            <span class="d-sm-block d-md-none d-lg-block">
                                <i class="fas fa-user-circle pr-1"></i>Profile
                            </span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('views.settings') }}">
                            <span class="d-block" data-toggle="tooltip" data-placement="bottom" title="Settings">
                                <i class="fas fa-cogs d-none d-md-block d-lg-none"></i>
                            </span>
                            <span class="d-sm-block d-md-none d-lg-block">
                                <i class="fas fa-cogs pr-1"></i>Settings
                            </span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.logout') }}">
                            <span class="d-block" data-toggle="tooltip" data-placement="bottom" title="Logout">
                                <i class="fas fa-sign-out-alt d-none d-md-block d-lg-none"></i>
                            </span>
                            <span class="d-sm-block d-md-none d-lg-block">
                                <i class="fas fa-sign-out-alt pr-1"></i><span class="d-lg-none">Logout</span>
                            </span>
                        </a>
                    </li>
                {% else %}
                    {% if registration_visible() %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('auth.register') }}">
                                <span class="d-block" data-toggle="tooltip" data-placement="bottom" title="Register">
                                    <i class="fas fa-user-plus d-none d-md-block d-lg-none"></i>
                                </span>
                                <span class="d-sm-block d-md-none d-lg-block">
                                    <i class="fas fa-user-plus pr-1"></i>Register
                                </span>
                            </a>
                        </li>
                    {% endif %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.login') }}">
                            <span class="d-block" data-toggle="tooltip" data-placement="bottom" title="Login">
                                <i class="fas fa-sign-in-alt d-none d-md-block d-lg-none"></i>
                            </span>
                            <span class="d-sm-block d-md-none d-lg-block">
                                <i class="fas fa-sign-in-alt pr-1"></i>Login
                            </span>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </div>
    </div>
</nav>