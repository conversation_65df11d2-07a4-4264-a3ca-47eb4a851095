# HELP ctfd_requests_total Total number of HTTP requests
# TYPE ctfd_requests_total counter
ctfd_requests_total 3448

# HELP ctfd_active_users Number of active users
# TYPE ctfd_active_users gauge
ctfd_active_users 216

# HELP ctfd_failed_logins_total Total failed login attempts
# TYPE ctfd_failed_logins_total counter
ctfd_failed_logins_total 87

# HELP ctfd_challenges_total Total number of challenges
# TYPE ctfd_challenges_total gauge
ctfd_challenges_total 72

# HELP ctfd_submissions_total Total challenge submissions
# TYPE ctfd_submissions_total counter
ctfd_submissions_total 1303

# HELP ctfd_response_time_seconds Average response time
# TYPE ctfd_response_time_seconds gauge
ctfd_response_time_seconds 0.538

# HELP ctfd_database_connections Active database connections
# TYPE ctfd_database_connections gauge
ctfd_database_connections 16

# HELP ctfd_memory_usage_bytes Memory usage in bytes
# TYPE ctfd_memory_usage_bytes gauge
ctfd_memory_usage_bytes 328998370

# HELP ctfd_cpu_usage_percent CPU usage percentage
# TYPE ctfd_cpu_usage_percent gauge
ctfd_cpu_usage_percent 57.3

# HELP ctfd_disk_usage_percent Disk usage percentage
# TYPE ctfd_disk_usage_percent gauge
ctfd_disk_usage_percent 27.3

# HELP ctfd_error_rate_percent Error rate percentage
# TYPE ctfd_error_rate_percent gauge
ctfd_error_rate_percent 4.29

# HELP ctfd_security_events_total Security events detected
# TYPE ctfd_security_events_total counter
ctfd_security_events_total 27

# HELP ctfd_container_status Container status (1=up, 0=down)
# TYPE ctfd_container_status gauge
ctfd_container_status{container="web"} 1
ctfd_container_status{container="db"} 1
ctfd_container_status{container="redis"} 1

# Generated at 2025-05-29 23:47:56
---
# HELP ctfd_requests_total Total number of HTTP requests
# TYPE ctfd_requests_total counter
ctfd_requests_total 3653

# HELP ctfd_active_users Number of active users
# TYPE ctfd_active_users gauge
ctfd_active_users 189

# HELP ctfd_failed_logins_total Total failed login attempts
# TYPE ctfd_failed_logins_total counter
ctfd_failed_logins_total 60

# HELP ctfd_challenges_total Total number of challenges
# TYPE ctfd_challenges_total gauge
ctfd_challenges_total 72

# HELP ctfd_submissions_total Total challenge submissions
# TYPE ctfd_submissions_total counter
ctfd_submissions_total 2524

# HELP ctfd_response_time_seconds Average response time
# TYPE ctfd_response_time_seconds gauge
ctfd_response_time_seconds 1.191

# HELP ctfd_database_connections Active database connections
# TYPE ctfd_database_connections gauge
ctfd_database_connections 11

# HELP ctfd_memory_usage_bytes Memory usage in bytes
# TYPE ctfd_memory_usage_bytes gauge
ctfd_memory_usage_bytes 529448773

# HELP ctfd_cpu_usage_percent CPU usage percentage
# TYPE ctfd_cpu_usage_percent gauge
ctfd_cpu_usage_percent 73.7

# HELP ctfd_disk_usage_percent Disk usage percentage
# TYPE ctfd_disk_usage_percent gauge
ctfd_disk_usage_percent 53.8

# HELP ctfd_error_rate_percent Error rate percentage
# TYPE ctfd_error_rate_percent gauge
ctfd_error_rate_percent 1.94

# HELP ctfd_security_events_total Security events detected
# TYPE ctfd_security_events_total counter
ctfd_security_events_total 25

# HELP ctfd_container_status Container status (1=up, 0=down)
# TYPE ctfd_container_status gauge
ctfd_container_status{container="web"} 1
ctfd_container_status{container="db"} 1
ctfd_container_status{container="redis"} 1

# Generated at 2025-05-29 23:47:56
---
# HELP ctfd_requests_total Total number of HTTP requests
# TYPE ctfd_requests_total counter
ctfd_requests_total 3449

# HELP ctfd_active_users Number of active users
# TYPE ctfd_active_users gauge
ctfd_active_users 179

# HELP ctfd_failed_logins_total Total failed login attempts
# TYPE ctfd_failed_logins_total counter
ctfd_failed_logins_total 58

# HELP ctfd_challenges_total Total number of challenges
# TYPE ctfd_challenges_total gauge
ctfd_challenges_total 72

# HELP ctfd_submissions_total Total challenge submissions
# TYPE ctfd_submissions_total counter
ctfd_submissions_total 1065

# HELP ctfd_response_time_seconds Average response time
# TYPE ctfd_response_time_seconds gauge
ctfd_response_time_seconds 0.191

# HELP ctfd_database_connections Active database connections
# TYPE ctfd_database_connections gauge
ctfd_database_connections 22

# HELP ctfd_memory_usage_bytes Memory usage in bytes
# TYPE ctfd_memory_usage_bytes gauge
ctfd_memory_usage_bytes 523174607

# HELP ctfd_cpu_usage_percent CPU usage percentage
# TYPE ctfd_cpu_usage_percent gauge
ctfd_cpu_usage_percent 65.5

# HELP ctfd_disk_usage_percent Disk usage percentage
# TYPE ctfd_disk_usage_percent gauge
ctfd_disk_usage_percent 84.5

# HELP ctfd_error_rate_percent Error rate percentage
# TYPE ctfd_error_rate_percent gauge
ctfd_error_rate_percent 3.69

# HELP ctfd_security_events_total Security events detected
# TYPE ctfd_security_events_total counter
ctfd_security_events_total 10

# HELP ctfd_container_status Container status (1=up, 0=down)
# TYPE ctfd_container_status gauge
ctfd_container_status{container="web"} 1
ctfd_container_status{container="db"} 1
ctfd_container_status{container="redis"} 1

# Generated at 2025-05-29 23:47:57
---
# HELP ctfd_requests_total Total number of HTTP requests
# TYPE ctfd_requests_total counter
ctfd_requests_total 3180

# HELP ctfd_active_users Number of active users
# TYPE ctfd_active_users gauge
ctfd_active_users 192

# HELP ctfd_failed_logins_total Total failed login attempts
# TYPE ctfd_failed_logins_total counter
ctfd_failed_logins_total 51

# HELP ctfd_challenges_total Total number of challenges
# TYPE ctfd_challenges_total gauge
ctfd_challenges_total 72

# HELP ctfd_submissions_total Total challenge submissions
# TYPE ctfd_submissions_total counter
ctfd_submissions_total 2315

# HELP ctfd_response_time_seconds Average response time
# TYPE ctfd_response_time_seconds gauge
ctfd_response_time_seconds 0.641

# HELP ctfd_database_connections Active database connections
# TYPE ctfd_database_connections gauge
ctfd_database_connections 16

# HELP ctfd_memory_usage_bytes Memory usage in bytes
# TYPE ctfd_memory_usage_bytes gauge
ctfd_memory_usage_bytes 103490743

# HELP ctfd_cpu_usage_percent CPU usage percentage
# TYPE ctfd_cpu_usage_percent gauge
ctfd_cpu_usage_percent 36.9

# HELP ctfd_disk_usage_percent Disk usage percentage
# TYPE ctfd_disk_usage_percent gauge
ctfd_disk_usage_percent 67.6

# HELP ctfd_error_rate_percent Error rate percentage
# TYPE ctfd_error_rate_percent gauge
ctfd_error_rate_percent 1.73

# HELP ctfd_security_events_total Security events detected
# TYPE ctfd_security_events_total counter
ctfd_security_events_total 43

# HELP ctfd_container_status Container status (1=up, 0=down)
# TYPE ctfd_container_status gauge
ctfd_container_status{container="web"} 1
ctfd_container_status{container="db"} 1
ctfd_container_status{container="redis"} 1

# Generated at 2025-05-29 23:47:57
---
# HELP ctfd_requests_total Total number of HTTP requests
# TYPE ctfd_requests_total counter
ctfd_requests_total 3600

# HELP ctfd_active_users Number of active users
# TYPE ctfd_active_users gauge
ctfd_active_users 240

# HELP ctfd_failed_logins_total Total failed login attempts
# TYPE ctfd_failed_logins_total counter
ctfd_failed_logins_total 99

# HELP ctfd_challenges_total Total number of challenges
# TYPE ctfd_challenges_total gauge
ctfd_challenges_total 72

# HELP ctfd_submissions_total Total challenge submissions
# TYPE ctfd_submissions_total counter
ctfd_submissions_total 1263

# HELP ctfd_response_time_seconds Average response time
# TYPE ctfd_response_time_seconds gauge
ctfd_response_time_seconds 1.370

# HELP ctfd_database_connections Active database connections
# TYPE ctfd_database_connections gauge
ctfd_database_connections 25

# HELP ctfd_memory_usage_bytes Memory usage in bytes
# TYPE ctfd_memory_usage_bytes gauge
ctfd_memory_usage_bytes 166395431

# HELP ctfd_cpu_usage_percent CPU usage percentage
# TYPE ctfd_cpu_usage_percent gauge
ctfd_cpu_usage_percent 75.8

# HELP ctfd_disk_usage_percent Disk usage percentage
# TYPE ctfd_disk_usage_percent gauge
ctfd_disk_usage_percent 38.3

# HELP ctfd_error_rate_percent Error rate percentage
# TYPE ctfd_error_rate_percent gauge
ctfd_error_rate_percent 2.04

# HELP ctfd_security_events_total Security events detected
# TYPE ctfd_security_events_total counter
ctfd_security_events_total 25

# HELP ctfd_container_status Container status (1=up, 0=down)
# TYPE ctfd_container_status gauge
ctfd_container_status{container="web"} 1
ctfd_container_status{container="db"} 1
ctfd_container_status{container="redis"} 1

# Generated at 2025-05-29 23:47:58
---
# HELP ctfd_requests_total Total number of HTTP requests
# TYPE ctfd_requests_total counter
ctfd_requests_total 3401

# HELP ctfd_active_users Number of active users
# TYPE ctfd_active_users gauge
ctfd_active_users 202

# HELP ctfd_failed_logins_total Total failed login attempts
# TYPE ctfd_failed_logins_total counter
ctfd_failed_logins_total 7

# HELP ctfd_challenges_total Total number of challenges
# TYPE ctfd_challenges_total gauge
ctfd_challenges_total 72

# HELP ctfd_submissions_total Total challenge submissions
# TYPE ctfd_submissions_total counter
ctfd_submissions_total 2667

# HELP ctfd_response_time_seconds Average response time
# TYPE ctfd_response_time_seconds gauge
ctfd_response_time_seconds 0.803

# HELP ctfd_database_connections Active database connections
# TYPE ctfd_database_connections gauge
ctfd_database_connections 12

# HELP ctfd_memory_usage_bytes Memory usage in bytes
# TYPE ctfd_memory_usage_bytes gauge
ctfd_memory_usage_bytes 111269693

# HELP ctfd_cpu_usage_percent CPU usage percentage
# TYPE ctfd_cpu_usage_percent gauge
ctfd_cpu_usage_percent 46.1

# HELP ctfd_disk_usage_percent Disk usage percentage
# TYPE ctfd_disk_usage_percent gauge
ctfd_disk_usage_percent 83.1

# HELP ctfd_error_rate_percent Error rate percentage
# TYPE ctfd_error_rate_percent gauge
ctfd_error_rate_percent 1.12

# HELP ctfd_security_events_total Security events detected
# TYPE ctfd_security_events_total counter
ctfd_security_events_total 26

# HELP ctfd_container_status Container status (1=up, 0=down)
# TYPE ctfd_container_status gauge
ctfd_container_status{container="web"} 1
ctfd_container_status{container="db"} 1
ctfd_container_status{container="redis"} 0

# Generated at 2025-05-29 23:47:58
---
# HELP ctfd_requests_total Total number of HTTP requests
# TYPE ctfd_requests_total counter
ctfd_requests_total 3445

# HELP ctfd_active_users Number of active users
# TYPE ctfd_active_users gauge
ctfd_active_users 203

# HELP ctfd_failed_logins_total Total failed login attempts
# TYPE ctfd_failed_logins_total counter
ctfd_failed_logins_total 24

# HELP ctfd_challenges_total Total number of challenges
# TYPE ctfd_challenges_total gauge
ctfd_challenges_total 72

# HELP ctfd_submissions_total Total challenge submissions
# TYPE ctfd_submissions_total counter
ctfd_submissions_total 1318

# HELP ctfd_response_time_seconds Average response time
# TYPE ctfd_response_time_seconds gauge
ctfd_response_time_seconds 0.239

# HELP ctfd_database_connections Active database connections
# TYPE ctfd_database_connections gauge
ctfd_database_connections 13

# HELP ctfd_memory_usage_bytes Memory usage in bytes
# TYPE ctfd_memory_usage_bytes gauge
ctfd_memory_usage_bytes 488811964

# HELP ctfd_cpu_usage_percent CPU usage percentage
# TYPE ctfd_cpu_usage_percent gauge
ctfd_cpu_usage_percent 60.2

# HELP ctfd_disk_usage_percent Disk usage percentage
# TYPE ctfd_disk_usage_percent gauge
ctfd_disk_usage_percent 58.3

# HELP ctfd_error_rate_percent Error rate percentage
# TYPE ctfd_error_rate_percent gauge
ctfd_error_rate_percent 4.00

# HELP ctfd_security_events_total Security events detected
# TYPE ctfd_security_events_total counter
ctfd_security_events_total 36

# HELP ctfd_container_status Container status (1=up, 0=down)
# TYPE ctfd_container_status gauge
ctfd_container_status{container="web"} 1
ctfd_container_status{container="db"} 0
ctfd_container_status{container="redis"} 1

# Generated at 2025-05-29 23:47:59
---
# HELP ctfd_requests_total Total number of HTTP requests
# TYPE ctfd_requests_total counter
ctfd_requests_total 3528

# HELP ctfd_active_users Number of active users
# TYPE ctfd_active_users gauge
ctfd_active_users 214

# HELP ctfd_failed_logins_total Total failed login attempts
# TYPE ctfd_failed_logins_total counter
ctfd_failed_logins_total 67

# HELP ctfd_challenges_total Total number of challenges
# TYPE ctfd_challenges_total gauge
ctfd_challenges_total 72

# HELP ctfd_submissions_total Total challenge submissions
# TYPE ctfd_submissions_total counter
ctfd_submissions_total 2486

# HELP ctfd_response_time_seconds Average response time
# TYPE ctfd_response_time_seconds gauge
ctfd_response_time_seconds 1.824

# HELP ctfd_database_connections Active database connections
# TYPE ctfd_database_connections gauge
ctfd_database_connections 16

# HELP ctfd_memory_usage_bytes Memory usage in bytes
# TYPE ctfd_memory_usage_bytes gauge
ctfd_memory_usage_bytes 298518142

# HELP ctfd_cpu_usage_percent CPU usage percentage
# TYPE ctfd_cpu_usage_percent gauge
ctfd_cpu_usage_percent 92.9

# HELP ctfd_disk_usage_percent Disk usage percentage
# TYPE ctfd_disk_usage_percent gauge
ctfd_disk_usage_percent 82.2

# HELP ctfd_error_rate_percent Error rate percentage
# TYPE ctfd_error_rate_percent gauge
ctfd_error_rate_percent 1.07

# HELP ctfd_security_events_total Security events detected
# TYPE ctfd_security_events_total counter
ctfd_security_events_total 6

# HELP ctfd_container_status Container status (1=up, 0=down)
# TYPE ctfd_container_status gauge
ctfd_container_status{container="web"} 1
ctfd_container_status{container="db"} 1
ctfd_container_status{container="redis"} 1

# Generated at 2025-05-29 23:47:59
---
# HELP ctfd_requests_total Total number of HTTP requests
# TYPE ctfd_requests_total counter
ctfd_requests_total 3565

# HELP ctfd_active_users Number of active users
# TYPE ctfd_active_users gauge
ctfd_active_users 190

# HELP ctfd_failed_logins_total Total failed login attempts
# TYPE ctfd_failed_logins_total counter
ctfd_failed_logins_total 13

# HELP ctfd_challenges_total Total number of challenges
# TYPE ctfd_challenges_total gauge
ctfd_challenges_total 72

# HELP ctfd_submissions_total Total challenge submissions
# TYPE ctfd_submissions_total counter
ctfd_submissions_total 2560

# HELP ctfd_response_time_seconds Average response time
# TYPE ctfd_response_time_seconds gauge
ctfd_response_time_seconds 1.623

# HELP ctfd_database_connections Active database connections
# TYPE ctfd_database_connections gauge
ctfd_database_connections 8

# HELP ctfd_memory_usage_bytes Memory usage in bytes
# TYPE ctfd_memory_usage_bytes gauge
ctfd_memory_usage_bytes 676426135

# HELP ctfd_cpu_usage_percent CPU usage percentage
# TYPE ctfd_cpu_usage_percent gauge
ctfd_cpu_usage_percent 64.7

# HELP ctfd_disk_usage_percent Disk usage percentage
# TYPE ctfd_disk_usage_percent gauge
ctfd_disk_usage_percent 54.3

# HELP ctfd_error_rate_percent Error rate percentage
# TYPE ctfd_error_rate_percent gauge
ctfd_error_rate_percent 0.75

# HELP ctfd_security_events_total Security events detected
# TYPE ctfd_security_events_total counter
ctfd_security_events_total 23

# HELP ctfd_container_status Container status (1=up, 0=down)
# TYPE ctfd_container_status gauge
ctfd_container_status{container="web"} 0
ctfd_container_status{container="db"} 1
ctfd_container_status{container="redis"} 0

# Generated at 2025-05-29 23:48:00
---
# HELP ctfd_requests_total Total number of HTTP requests
# TYPE ctfd_requests_total counter
ctfd_requests_total 3677

# HELP ctfd_active_users Number of active users
# TYPE ctfd_active_users gauge
ctfd_active_users 179

# HELP ctfd_failed_logins_total Total failed login attempts
# TYPE ctfd_failed_logins_total counter
ctfd_failed_logins_total 43

# HELP ctfd_challenges_total Total number of challenges
# TYPE ctfd_challenges_total gauge
ctfd_challenges_total 72

# HELP ctfd_submissions_total Total challenge submissions
# TYPE ctfd_submissions_total counter
ctfd_submissions_total 2978

# HELP ctfd_response_time_seconds Average response time
# TYPE ctfd_response_time_seconds gauge
ctfd_response_time_seconds 0.240

# HELP ctfd_database_connections Active database connections
# TYPE ctfd_database_connections gauge
ctfd_database_connections 11

# HELP ctfd_memory_usage_bytes Memory usage in bytes
# TYPE ctfd_memory_usage_bytes gauge
ctfd_memory_usage_bytes 627128704

# HELP ctfd_cpu_usage_percent CPU usage percentage
# TYPE ctfd_cpu_usage_percent gauge
ctfd_cpu_usage_percent 50.9

# HELP ctfd_disk_usage_percent Disk usage percentage
# TYPE ctfd_disk_usage_percent gauge
ctfd_disk_usage_percent 59.1

# HELP ctfd_error_rate_percent Error rate percentage
# TYPE ctfd_error_rate_percent gauge
ctfd_error_rate_percent 3.30

# HELP ctfd_security_events_total Security events detected
# TYPE ctfd_security_events_total counter
ctfd_security_events_total 22

# HELP ctfd_container_status Container status (1=up, 0=down)
# TYPE ctfd_container_status gauge
ctfd_container_status{container="web"} 0
ctfd_container_status{container="db"} 0
ctfd_container_status{container="redis"} 1

# Generated at 2025-05-29 23:48:00
---
# HELP ctfd_requests_total Total number of HTTP requests
# TYPE ctfd_requests_total counter
ctfd_requests_total 3133

# HELP ctfd_active_users Number of active users
# TYPE ctfd_active_users gauge
ctfd_active_users 176

# HELP ctfd_failed_logins_total Total failed login attempts
# TYPE ctfd_failed_logins_total counter
ctfd_failed_logins_total 70

# HELP ctfd_challenges_total Total number of challenges
# TYPE ctfd_challenges_total gauge
ctfd_challenges_total 72

# HELP ctfd_submissions_total Total challenge submissions
# TYPE ctfd_submissions_total counter
ctfd_submissions_total 2382

# HELP ctfd_response_time_seconds Average response time
# TYPE ctfd_response_time_seconds gauge
ctfd_response_time_seconds 0.254

# HELP ctfd_database_connections Active database connections
# TYPE ctfd_database_connections gauge
ctfd_database_connections 12

# HELP ctfd_memory_usage_bytes Memory usage in bytes
# TYPE ctfd_memory_usage_bytes gauge
ctfd_memory_usage_bytes 112045345

# HELP ctfd_cpu_usage_percent CPU usage percentage
# TYPE ctfd_cpu_usage_percent gauge
ctfd_cpu_usage_percent 27.1

# HELP ctfd_disk_usage_percent Disk usage percentage
# TYPE ctfd_disk_usage_percent gauge
ctfd_disk_usage_percent 46.9

# HELP ctfd_error_rate_percent Error rate percentage
# TYPE ctfd_error_rate_percent gauge
ctfd_error_rate_percent 4.37

# HELP ctfd_security_events_total Security events detected
# TYPE ctfd_security_events_total counter
ctfd_security_events_total 7

# HELP ctfd_container_status Container status (1=up, 0=down)
# TYPE ctfd_container_status gauge
ctfd_container_status{container="web"} 0
ctfd_container_status{container="db"} 0
ctfd_container_status{container="redis"} 0

# Generated at 2025-05-29 23:48:01
---
# HELP ctfd_requests_total Total number of HTTP requests
# TYPE ctfd_requests_total counter
ctfd_requests_total 3604

# HELP ctfd_active_users Number of active users
# TYPE ctfd_active_users gauge
ctfd_active_users 215

# HELP ctfd_failed_logins_total Total failed login attempts
# TYPE ctfd_failed_logins_total counter
ctfd_failed_logins_total 38

# HELP ctfd_challenges_total Total number of challenges
# TYPE ctfd_challenges_total gauge
ctfd_challenges_total 72

# HELP ctfd_submissions_total Total challenge submissions
# TYPE ctfd_submissions_total counter
ctfd_submissions_total 2774

# HELP ctfd_response_time_seconds Average response time
# TYPE ctfd_response_time_seconds gauge
ctfd_response_time_seconds 1.762

# HELP ctfd_database_connections Active database connections
# TYPE ctfd_database_connections gauge
ctfd_database_connections 17

# HELP ctfd_memory_usage_bytes Memory usage in bytes
# TYPE ctfd_memory_usage_bytes gauge
ctfd_memory_usage_bytes 384991061

# HELP ctfd_cpu_usage_percent CPU usage percentage
# TYPE ctfd_cpu_usage_percent gauge
ctfd_cpu_usage_percent 10.3

# HELP ctfd_disk_usage_percent Disk usage percentage
# TYPE ctfd_disk_usage_percent gauge
ctfd_disk_usage_percent 64.3

# HELP ctfd_error_rate_percent Error rate percentage
# TYPE ctfd_error_rate_percent gauge
ctfd_error_rate_percent 0.97

# HELP ctfd_security_events_total Security events detected
# TYPE ctfd_security_events_total counter
ctfd_security_events_total 41

# HELP ctfd_container_status Container status (1=up, 0=down)
# TYPE ctfd_container_status gauge
ctfd_container_status{container="web"} 1
ctfd_container_status{container="db"} 1
ctfd_container_status{container="redis"} 1

# Generated at 2025-05-29 23:48:01
---
# HELP ctfd_requests_total Total number of HTTP requests
# TYPE ctfd_requests_total counter
ctfd_requests_total 3314

# HELP ctfd_active_users Number of active users
# TYPE ctfd_active_users gauge
ctfd_active_users 196

# HELP ctfd_failed_logins_total Total failed login attempts
# TYPE ctfd_failed_logins_total counter
ctfd_failed_logins_total 58

# HELP ctfd_challenges_total Total number of challenges
# TYPE ctfd_challenges_total gauge
ctfd_challenges_total 72

# HELP ctfd_submissions_total Total challenge submissions
# TYPE ctfd_submissions_total counter
ctfd_submissions_total 1495

# HELP ctfd_response_time_seconds Average response time
# TYPE ctfd_response_time_seconds gauge
ctfd_response_time_seconds 1.075

# HELP ctfd_database_connections Active database connections
# TYPE ctfd_database_connections gauge
ctfd_database_connections 13

# HELP ctfd_memory_usage_bytes Memory usage in bytes
# TYPE ctfd_memory_usage_bytes gauge
ctfd_memory_usage_bytes 142166533

# HELP ctfd_cpu_usage_percent CPU usage percentage
# TYPE ctfd_cpu_usage_percent gauge
ctfd_cpu_usage_percent 53.0

# HELP ctfd_disk_usage_percent Disk usage percentage
# TYPE ctfd_disk_usage_percent gauge
ctfd_disk_usage_percent 40.1

# HELP ctfd_error_rate_percent Error rate percentage
# TYPE ctfd_error_rate_percent gauge
ctfd_error_rate_percent 1.74

# HELP ctfd_security_events_total Security events detected
# TYPE ctfd_security_events_total counter
ctfd_security_events_total 48

# HELP ctfd_container_status Container status (1=up, 0=down)
# TYPE ctfd_container_status gauge
ctfd_container_status{container="web"} 1
ctfd_container_status{container="db"} 1
ctfd_container_status{container="redis"} 1

# Generated at 2025-05-29 23:48:02
---
# HELP ctfd_requests_total Total number of HTTP requests
# TYPE ctfd_requests_total counter
ctfd_requests_total 3308

# HELP ctfd_active_users Number of active users
# TYPE ctfd_active_users gauge
ctfd_active_users 230

# HELP ctfd_failed_logins_total Total failed login attempts
# TYPE ctfd_failed_logins_total counter
ctfd_failed_logins_total 20

# HELP ctfd_challenges_total Total number of challenges
# TYPE ctfd_challenges_total gauge
ctfd_challenges_total 72

# HELP ctfd_submissions_total Total challenge submissions
# TYPE ctfd_submissions_total counter
ctfd_submissions_total 1156

# HELP ctfd_response_time_seconds Average response time
# TYPE ctfd_response_time_seconds gauge
ctfd_response_time_seconds 0.865

# HELP ctfd_database_connections Active database connections
# TYPE ctfd_database_connections gauge
ctfd_database_connections 10

# HELP ctfd_memory_usage_bytes Memory usage in bytes
# TYPE ctfd_memory_usage_bytes gauge
ctfd_memory_usage_bytes 656380916

# HELP ctfd_cpu_usage_percent CPU usage percentage
# TYPE ctfd_cpu_usage_percent gauge
ctfd_cpu_usage_percent 90.6

# HELP ctfd_disk_usage_percent Disk usage percentage
# TYPE ctfd_disk_usage_percent gauge
ctfd_disk_usage_percent 59.8

# HELP ctfd_error_rate_percent Error rate percentage
# TYPE ctfd_error_rate_percent gauge
ctfd_error_rate_percent 3.53

# HELP ctfd_security_events_total Security events detected
# TYPE ctfd_security_events_total counter
ctfd_security_events_total 5

# HELP ctfd_container_status Container status (1=up, 0=down)
# TYPE ctfd_container_status gauge
ctfd_container_status{container="web"} 1
ctfd_container_status{container="db"} 0
ctfd_container_status{container="redis"} 1

# Generated at 2025-05-29 23:48:02
---
# HELP ctfd_requests_total Total number of HTTP requests
# TYPE ctfd_requests_total counter
ctfd_requests_total 3323

# HELP ctfd_active_users Number of active users
# TYPE ctfd_active_users gauge
ctfd_active_users 175

# HELP ctfd_failed_logins_total Total failed login attempts
# TYPE ctfd_failed_logins_total counter
ctfd_failed_logins_total 91

# HELP ctfd_challenges_total Total number of challenges
# TYPE ctfd_challenges_total gauge
ctfd_challenges_total 72

# HELP ctfd_submissions_total Total challenge submissions
# TYPE ctfd_submissions_total counter
ctfd_submissions_total 2295

# HELP ctfd_response_time_seconds Average response time
# TYPE ctfd_response_time_seconds gauge
ctfd_response_time_seconds 1.654

# HELP ctfd_database_connections Active database connections
# TYPE ctfd_database_connections gauge
ctfd_database_connections 19

# HELP ctfd_memory_usage_bytes Memory usage in bytes
# TYPE ctfd_memory_usage_bytes gauge
ctfd_memory_usage_bytes 231290530

# HELP ctfd_cpu_usage_percent CPU usage percentage
# TYPE ctfd_cpu_usage_percent gauge
ctfd_cpu_usage_percent 80.7

# HELP ctfd_disk_usage_percent Disk usage percentage
# TYPE ctfd_disk_usage_percent gauge
ctfd_disk_usage_percent 56.6

# HELP ctfd_error_rate_percent Error rate percentage
# TYPE ctfd_error_rate_percent gauge
ctfd_error_rate_percent 0.85

# HELP ctfd_security_events_total Security events detected
# TYPE ctfd_security_events_total counter
ctfd_security_events_total 50

# HELP ctfd_container_status Container status (1=up, 0=down)
# TYPE ctfd_container_status gauge
ctfd_container_status{container="web"} 1
ctfd_container_status{container="db"} 1
ctfd_container_status{container="redis"} 1

# Generated at 2025-05-29 23:48:03
---
# HELP ctfd_requests_total Total number of HTTP requests
# TYPE ctfd_requests_total counter
ctfd_requests_total 3454

# HELP ctfd_active_users Number of active users
# TYPE ctfd_active_users gauge
ctfd_active_users 204

# HELP ctfd_failed_logins_total Total failed login attempts
# TYPE ctfd_failed_logins_total counter
ctfd_failed_logins_total 52

# HELP ctfd_challenges_total Total number of challenges
# TYPE ctfd_challenges_total gauge
ctfd_challenges_total 72

# HELP ctfd_submissions_total Total challenge submissions
# TYPE ctfd_submissions_total counter
ctfd_submissions_total 2316

# HELP ctfd_response_time_seconds Average response time
# TYPE ctfd_response_time_seconds gauge
ctfd_response_time_seconds 1.594

# HELP ctfd_database_connections Active database connections
# TYPE ctfd_database_connections gauge
ctfd_database_connections 18

# HELP ctfd_memory_usage_bytes Memory usage in bytes
# TYPE ctfd_memory_usage_bytes gauge
ctfd_memory_usage_bytes 621104145

# HELP ctfd_cpu_usage_percent CPU usage percentage
# TYPE ctfd_cpu_usage_percent gauge
ctfd_cpu_usage_percent 51.2

# HELP ctfd_disk_usage_percent Disk usage percentage
# TYPE ctfd_disk_usage_percent gauge
ctfd_disk_usage_percent 73.9

# HELP ctfd_error_rate_percent Error rate percentage
# TYPE ctfd_error_rate_percent gauge
ctfd_error_rate_percent 4.42

# HELP ctfd_security_events_total Security events detected
# TYPE ctfd_security_events_total counter
ctfd_security_events_total 2

# HELP ctfd_container_status Container status (1=up, 0=down)
# TYPE ctfd_container_status gauge
ctfd_container_status{container="web"} 1
ctfd_container_status{container="db"} 1
ctfd_container_status{container="redis"} 0

# Generated at 2025-05-29 23:48:03
---
# HELP ctfd_requests_total Total number of HTTP requests
# TYPE ctfd_requests_total counter
ctfd_requests_total 3706

# HELP ctfd_active_users Number of active users
# TYPE ctfd_active_users gauge
ctfd_active_users 225

# HELP ctfd_failed_logins_total Total failed login attempts
# TYPE ctfd_failed_logins_total counter
ctfd_failed_logins_total 21

# HELP ctfd_challenges_total Total number of challenges
# TYPE ctfd_challenges_total gauge
ctfd_challenges_total 72

# HELP ctfd_submissions_total Total challenge submissions
# TYPE ctfd_submissions_total counter
ctfd_submissions_total 1313

# HELP ctfd_response_time_seconds Average response time
# TYPE ctfd_response_time_seconds gauge
ctfd_response_time_seconds 0.094

# HELP ctfd_database_connections Active database connections
# TYPE ctfd_database_connections gauge
ctfd_database_connections 20

# HELP ctfd_memory_usage_bytes Memory usage in bytes
# TYPE ctfd_memory_usage_bytes gauge
ctfd_memory_usage_bytes 712248718

# HELP ctfd_cpu_usage_percent CPU usage percentage
# TYPE ctfd_cpu_usage_percent gauge
ctfd_cpu_usage_percent 10.9

# HELP ctfd_disk_usage_percent Disk usage percentage
# TYPE ctfd_disk_usage_percent gauge
ctfd_disk_usage_percent 30.3

# HELP ctfd_error_rate_percent Error rate percentage
# TYPE ctfd_error_rate_percent gauge
ctfd_error_rate_percent 1.72

# HELP ctfd_security_events_total Security events detected
# TYPE ctfd_security_events_total counter
ctfd_security_events_total 26

# HELP ctfd_container_status Container status (1=up, 0=down)
# TYPE ctfd_container_status gauge
ctfd_container_status{container="web"} 1
ctfd_container_status{container="db"} 1
ctfd_container_status{container="redis"} 1

# Generated at 2025-05-29 23:48:04
---
# HELP ctfd_requests_total Total number of HTTP requests
# TYPE ctfd_requests_total counter
ctfd_requests_total 3361

# HELP ctfd_active_users Number of active users
# TYPE ctfd_active_users gauge
ctfd_active_users 210

# HELP ctfd_failed_logins_total Total failed login attempts
# TYPE ctfd_failed_logins_total counter
ctfd_failed_logins_total 16

# HELP ctfd_challenges_total Total number of challenges
# TYPE ctfd_challenges_total gauge
ctfd_challenges_total 72

# HELP ctfd_submissions_total Total challenge submissions
# TYPE ctfd_submissions_total counter
ctfd_submissions_total 1756

# HELP ctfd_response_time_seconds Average response time
# TYPE ctfd_response_time_seconds gauge
ctfd_response_time_seconds 0.588

# HELP ctfd_database_connections Active database connections
# TYPE ctfd_database_connections gauge
ctfd_database_connections 25

# HELP ctfd_memory_usage_bytes Memory usage in bytes
# TYPE ctfd_memory_usage_bytes gauge
ctfd_memory_usage_bytes 481804552

# HELP ctfd_cpu_usage_percent CPU usage percentage
# TYPE ctfd_cpu_usage_percent gauge
ctfd_cpu_usage_percent 94.6

# HELP ctfd_disk_usage_percent Disk usage percentage
# TYPE ctfd_disk_usage_percent gauge
ctfd_disk_usage_percent 22.7

# HELP ctfd_error_rate_percent Error rate percentage
# TYPE ctfd_error_rate_percent gauge
ctfd_error_rate_percent 4.10

# HELP ctfd_security_events_total Security events detected
# TYPE ctfd_security_events_total counter
ctfd_security_events_total 24

# HELP ctfd_container_status Container status (1=up, 0=down)
# TYPE ctfd_container_status gauge
ctfd_container_status{container="web"} 0
ctfd_container_status{container="db"} 1
ctfd_container_status{container="redis"} 1

# Generated at 2025-05-29 23:48:04
---
# HELP ctfd_requests_total Total number of HTTP requests
# TYPE ctfd_requests_total counter
ctfd_requests_total 3503

# HELP ctfd_active_users Number of active users
# TYPE ctfd_active_users gauge
ctfd_active_users 211

# HELP ctfd_failed_logins_total Total failed login attempts
# TYPE ctfd_failed_logins_total counter
ctfd_failed_logins_total 68

# HELP ctfd_challenges_total Total number of challenges
# TYPE ctfd_challenges_total gauge
ctfd_challenges_total 72

# HELP ctfd_submissions_total Total challenge submissions
# TYPE ctfd_submissions_total counter
ctfd_submissions_total 2298

# HELP ctfd_response_time_seconds Average response time
# TYPE ctfd_response_time_seconds gauge
ctfd_response_time_seconds 1.943

# HELP ctfd_database_connections Active database connections
# TYPE ctfd_database_connections gauge
ctfd_database_connections 19

# HELP ctfd_memory_usage_bytes Memory usage in bytes
# TYPE ctfd_memory_usage_bytes gauge
ctfd_memory_usage_bytes 797356189

# HELP ctfd_cpu_usage_percent CPU usage percentage
# TYPE ctfd_cpu_usage_percent gauge
ctfd_cpu_usage_percent 80.8

# HELP ctfd_disk_usage_percent Disk usage percentage
# TYPE ctfd_disk_usage_percent gauge
ctfd_disk_usage_percent 42.9

# HELP ctfd_error_rate_percent Error rate percentage
# TYPE ctfd_error_rate_percent gauge
ctfd_error_rate_percent 4.85

# HELP ctfd_security_events_total Security events detected
# TYPE ctfd_security_events_total counter
ctfd_security_events_total 49

# HELP ctfd_container_status Container status (1=up, 0=down)
# TYPE ctfd_container_status gauge
ctfd_container_status{container="web"} 0
ctfd_container_status{container="db"} 1
ctfd_container_status{container="redis"} 0

# Generated at 2025-05-29 23:48:05
---
# HELP ctfd_requests_total Total number of HTTP requests
# TYPE ctfd_requests_total counter
ctfd_requests_total 3236

# HELP ctfd_active_users Number of active users
# TYPE ctfd_active_users gauge
ctfd_active_users 182

# HELP ctfd_failed_logins_total Total failed login attempts
# TYPE ctfd_failed_logins_total counter
ctfd_failed_logins_total 76

# HELP ctfd_challenges_total Total number of challenges
# TYPE ctfd_challenges_total gauge
ctfd_challenges_total 72

# HELP ctfd_submissions_total Total challenge submissions
# TYPE ctfd_submissions_total counter
ctfd_submissions_total 2011

# HELP ctfd_response_time_seconds Average response time
# TYPE ctfd_response_time_seconds gauge
ctfd_response_time_seconds 0.688

# HELP ctfd_database_connections Active database connections
# TYPE ctfd_database_connections gauge
ctfd_database_connections 21

# HELP ctfd_memory_usage_bytes Memory usage in bytes
# TYPE ctfd_memory_usage_bytes gauge
ctfd_memory_usage_bytes 541680992

# HELP ctfd_cpu_usage_percent CPU usage percentage
# TYPE ctfd_cpu_usage_percent gauge
ctfd_cpu_usage_percent 89.1

# HELP ctfd_disk_usage_percent Disk usage percentage
# TYPE ctfd_disk_usage_percent gauge
ctfd_disk_usage_percent 55.7

# HELP ctfd_error_rate_percent Error rate percentage
# TYPE ctfd_error_rate_percent gauge
ctfd_error_rate_percent 4.02

# HELP ctfd_security_events_total Security events detected
# TYPE ctfd_security_events_total counter
ctfd_security_events_total 47

# HELP ctfd_container_status Container status (1=up, 0=down)
# TYPE ctfd_container_status gauge
ctfd_container_status{container="web"} 0
ctfd_container_status{container="db"} 0
ctfd_container_status{container="redis"} 1

# Generated at 2025-05-29 23:48:05
---
