/* Custom class for access instructions */
.access-instructions {
    color: #ffffff !important;
    text-shadow: none;
}

.card-body p {
    color: #e2e5e9 !important;
}

/* Web Desktop Plugin Styles - Flat design */

/* General Styles */
.desktop-container {
    margin-top: 20px;
    margin-bottom: 30px;
}

/* Template Selection Styles */
.template-header {
    margin-bottom: 25px;
    padding: 20px;
    background-color: var(--wd-bg-secondary);
    border-radius: 10px;
    border: 1px solid var(--wd-border-medium);
    color: var(--wd-text-primary);
}

.template-grid {
    margin-top: 20px;
}

.template-card {
    transition: all 0.3s ease;
    border: 1px solid var(--wd-border-medium);
    border-radius: 8px;
    overflow: hidden;
    background-color: var(--wd-bg-primary);
}

.template-card:hover {
    transform: translateY(-5px);
    border-color: var(--wd-text-accent);
}

.template-card .card-header {
    font-weight: bold;
    display: flex;
    align-items: center;
    background-color: var(--wd-bg-secondary);
    border-bottom: 2px solid var(--wd-border-light);
    padding: 12px 15px;
    color: var(--wd-text-primary);
}

.template-title {
    display: flex;
    align-items: center;
    color: var(--wd-text-primary);
}

.template-title img {
    width: 28px;
    height: 28px;
    margin-right: 12px;
}

.template-icon {
    width: 90px;
    height: 90px;
    margin: 20px auto;
    display: block;
    transition: transform 0.3s ease;
    filter: var(--wd-icon-filter);
}

.template-card:hover .template-icon {
    transform: scale(1.1);
}

.template-description {
    min-height: 60px;
    padding: 0 15px;
    color: #d4d6d8;
    font-size: 0.95em;
    line-height: 1.5;
    margin-bottom: 15px;
}

.template-specs {
    color: #a8abb0;
    font-size: 0.9em;
    margin-bottom: 15px;
}

.spec-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.spec-item i {
    width: 20px;
    margin-right: 10px;
    color: #ffffff;
}

.launch-btn {
    width: 100%;
    border-radius: 6px;
    font-weight: 600;
    padding: 10px 16px;
    transition: all 0.2s ease;
}

.launch-btn:hover {
    transform: translateY(-2px);
}

/* Active Desktop Styles */
.desktop-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    border-radius: var(--wd-radius-md);
}

.desktop-header-icon {
    width: 48px;
    height: 48px;
    margin-right: 15px;
    filter: var(--wd-icon-filter-light);
}

.desktop-header h3 {
    margin: 0;
    font-size: 1.5rem;
    color: white; /* Always white for contrast on primary background */
}

.desktop-info-card {
    background-color: var(--wd-bg-secondary);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid var(--wd-border-light);
}

.desktop-info-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.desktop-info-item:last-child {
    margin-bottom: 0;
}

.desktop-info-item i {
    width: 30px;
    font-size: 1.2rem;
    color: var(--wd-text-accent);
    text-align: center;
    margin-right: 10px;
}

.info-label {
    font-size: 0.85rem;
    color: var(--wd-text-secondary);
    display: block;
    margin-bottom: 2px;
}

.info-value {
    font-weight: 600;
    color: var(--wd-text-primary);
}

.desktop-actions {
    margin-top: 25px;
}

.action-buttons-row {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    gap: 10px;
}

.action-form {
    flex: 1;
    margin: 0;
}

.action-form button {
    width: 100%;
    transition: all 0.3s ease;
}

.action-form button:hover {
    transform: translateY(-2px);
}

.desktop-credentials {
    background-color: var(--wd-bg-warning);
    border-radius: 8px;
    padding: 12px;
    border: 2px solid var(--wd-warning);
}

.desktop-credentials .info-label {
    color: var(--wd-warning-text);
    font-weight: bold;
}

.desktop-controls {
    background-color: var(--wd-bg-primary);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 25px;
    border: 1px solid var(--wd-border-medium);
}

.direct-launch-btn {
    margin-top: 10px;
    width: 100%;
    font-weight: 600;
    padding: 15px;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    border-radius: 8px;
}

.direct-launch-btn:hover {
    transform: translateY(-3px);
}

.status-badge {
    margin-left: 10px;
    padding: 5px 12px;
    border-radius: 30px;
    font-size: 0.8em;
    display: inline-block;
    font-weight: 600;
}

.status-running {
    background-color: var(--wd-success);
    color: white;
    border: none;
}

.status-starting {
    background-color: var(--wd-warning);
    color: var(--wd-warning-text);
}

.status-stopped {
    background-color: var(--wd-danger);
    color: white;
}

.status-expired {
    background-color: var(--wd-text-secondary);
    color: white;
}

.status-expired_renewable {
    background-color: var(--wd-info);
    color: white;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    flex-direction: column;
}

.spinner {
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced UI Styles */
.template-header {
    margin-bottom: 30px;
    padding: 20px;
    border-radius: 10px;
}

/* Recommended template styling */
.border-primary {
    border-width: 2px;
}

.badge-light {
    background-color: rgba(255, 255, 255, 0.9);
    color: var(--wd-text-accent);
    font-weight: bold;
}

/* Shadow effects - removed */
.shadow-sm {
    box-shadow: none !important;
}

.shadow {
    box-shadow: none !important;
}

/* Admin Styles */
.template-card .card-footer {
    background-color: var(--wd-bg-secondary);
    border-top: 1px solid var(--wd-border-light);
    padding: 15px;
}

.template-details {
    margin-top: 15px;
}

.detail-item {
    display: flex;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.detail-label {
    width: 80px;
    color: var(--wd-text-secondary);
    display: flex;
    align-items: center;
}

.detail-label i {
    margin-right: 5px;
    width: 16px;
}

.detail-value {
    flex: 1;
    font-family: monospace;
    word-break: break-all;
    color: var(--wd-text-primary);
}

/* Container Management Styles */
.container-card {
    transition: all 0.3s ease;
    background-color: var(--wd-bg-primary);
    border: 1px solid var(--wd-border-medium);
}

.container-card:hover {
    border-color: var(--wd-text-accent);
}

.container-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--wd-text-primary);
}

.container-info {
    margin-bottom: 10px;
    color: var(--wd-text-secondary);
}

.settings-form .card {
    margin-bottom: 25px;
    background-color: var(--wd-bg-primary);
    border: 1px solid var(--wd-border-medium);
}

/* Challenge info card */
.challenge-info-card {
    border: 2px solid var(--wd-info) !important;
}

.challenge-info-card .card-header {
    background-color: var(--wd-info) !important;
    color: white !important;
    font-weight: bold;
}

.challenge-info-card code {
    background-color: var(--wd-bg-tertiary);
    color: var(--wd-text-accent);
    padding: 4px 8px;
    border-radius: 4px;
}

/* Alert colors for dark mode */
.alert-success {
    background-color: var(--wd-bg-success);
    color: var(--wd-success-text);
    border-color: var(--wd-success-border);
}

.alert-warning {
    background-color: var(--wd-bg-warning);
    color: var(--wd-warning-text);
    border-color: var(--wd-warning-border);
}

.alert-danger {
    background-color: var(--wd-bg-danger);
    color: var(--wd-danger-text);
    border-color: var(--wd-danger-border);
}

.alert-info {
    background-color: var(--wd-bg-info);
    color: var(--wd-info-text);
    border-color: var(--wd-info-border);
}

/* Flat button styles (replacing neumorphic) */
.btn-neu {
    border: 1px solid var(--wd-border-medium);
    border-radius: 6px;
    background-color: var(--wd-bg-secondary);
    transition: all 0.3s ease;
}

.btn-neu:hover {
    background-color: var(--wd-bg-tertiary);
    transform: translateY(-2px);
}

.btn-neu:active {
    background-color: var(--wd-bg-tertiary);
    transform: translateY(0);
}

/* Flat card styles (replacing neumorphic) */
.card-neu {
    border: 1px solid var(--wd-border-medium);
    border-radius: 8px;
    background-color: var(--wd-bg-primary);
}

.card-neu .card-header {
    background-color: var(--wd-bg-secondary);
    border-bottom: 1px solid var(--wd-border-light);
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.card-neu .card-body {
    color: var(--wd-text-primary);
}

.card-neu .card-footer {
    background-color: var(--wd-bg-secondary);
    border-top: 1px solid var(--wd-border-light);
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

/* Improve jumbotron appearance in dark mode */
.jumbotron {
    background-color: var(--primary) !important;
    color: white !important;
    border-radius: 10px;
}

/* Improved loading animation inspired by the challenge loading */
.wd-loading-spinner {
    display: inline-block;
    width: 80px;
    height: 80px;
    margin: 0 auto;
}

.wd-loading-spinner:after {
    content: " ";
    display: block;
    width: 64px;
    height: 64px;
    margin: 8px;
    border-radius: 50%;
    border: 6px solid var(--wd-text-accent);
    border-color: var(--wd-text-accent) transparent var(--wd-text-accent) transparent;
    animation: wd-loading-spinner 1.2s linear infinite;
}

@keyframes wd-loading-spinner {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Progress bar animation for loading */
.wd-progress-bar {
    width: 100%;
    height: 8px;
    background-color: var(--wd-bg-secondary);
    border-radius: 4px;
    margin: 15px 0;
    overflow: hidden;
    position: relative;
}

.wd-progress-bar:before {
    content: '';
    position: absolute;
    left: -100%;
    top: 0;
    height: 100%;
    width: 50%;
    background: linear-gradient(to right, transparent, var(--wd-text-accent), transparent);
    animation: wd-progress 2s linear infinite;
}

@keyframes wd-progress {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Remove pulsating effect for buttons */
.btn-pulse {
    animation: none;
}

/* Add a global style for all anchor tags in the Web Desktop plugin */
.desktop-container a,
.template-card a,
.desktop-info-card a,
.desktop-controls a,
.container-card a {
    color: #ffffff !important;
    text-decoration: none;
    transition: all 0.2s ease;
}

.desktop-container a:hover,
.template-card a:hover,
.desktop-info-card a:hover,
.desktop-controls a:hover,
.container-card a:hover {
    text-decoration: underline;
}
