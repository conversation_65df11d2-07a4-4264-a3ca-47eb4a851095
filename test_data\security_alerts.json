[{"timestamp": "2025-05-29T23:24:38.801809", "severity": "critical", "title": "Multiple Failed Login Attempts", "message": "IP ************ has 15 failed login attempts in 5 minutes", "ip_address": "************"}, {"timestamp": "2025-05-29T21:24:38.801809", "severity": "warning", "title": "Rate Limit Exceeded", "message": "IP ************* exceeded rate limits on /challenges endpoint", "ip_address": "*************"}, {"timestamp": "2025-05-29T17:24:38.801809", "severity": "info", "title": "New User Registration Spike", "message": "10 new users registered in the last hour", "ip_address": null}]