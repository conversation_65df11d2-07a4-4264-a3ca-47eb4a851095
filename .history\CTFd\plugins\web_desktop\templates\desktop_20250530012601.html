{% extends "base.html" %}

<!-- Immediate script to hide loading overlay -->
<script>
    // Hide loading overlay as early as possible
    window.addEventListener('load', function() {
        var loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
            console.log('Hiding loading overlay on window load');
        }
    });

    // Also try to hide it immediately
    document.addEventListener('DOMContentLoaded', function() {
        var loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
            console.log('Hiding loading overlay on DOMContentLoaded');
        }
    });
</script>

{% block stylesheets %}
<link rel="stylesheet" href="{{ url_for('plugins.web_desktop.assets', path='css/webdesktop.css') }}">
<link rel="stylesheet" href="{{ url_for('plugins.web_desktop.assets', path='css/custom_styles.css') }}">
<style>
    /* Hide loading overlay by default */
    #loading-overlay {
        display: none !important;
    }

    /* Improved text colors for dark backgrounds */
    .desktop-info-card {
        background-color: rgba(0, 0, 0, 0.2) !important;
        border: 1px solid rgba(255, 255, 255, 0.1) !important;
    }

    .info-label {
        font-weight: bold !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
    }

    .info-value {
        color: #ffffff !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
    }
    @keyframes challenge-spin {
        to { transform: rotate(360deg); }
    }

    @keyframes progress {
        0% { width: 0%; }
        100% { width: 100%; }
    }

    /* Removed wd-progress animation */

    @keyframes wd-progress-stripes {
        0% { background-position: 0 0; }
        100% { background-position: 30px 0; }
    }

    /* Classes for progress bar states */
    .progress-complete {
        width: 100% !important;
        background-color: #28a745 !important;
        animation: none !important;
        transition: width 2s ease-in-out !important;
        transition-delay: 0.5s !important;
    }

    .progress-error {
        width: 100% !important;
        background-color: #dc3545 !important;
        animation: none !important;
        transition: width 2s ease-in-out !important;
        transition-delay: 0.5s !important;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }

    .desktop-credentials {
        background-color: #fff3cd;
        border-radius: 5px;
        padding: 10px;
        margin-top: 10px;
        border: 2px solid #ffc107;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }

    .desktop-credentials .info-label {
        color: #856404;
        font-weight: bold;
    }

    .challenge-info-card {
        border: 2px solid #17a2b8;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }

    .challenge-info-card .card-header {
        font-weight: bold;
    }

    .challenge-info-card code {
        background-color: #343a40;
        color: #ffffff;
        padding: 4px 8px;
        border-radius: 4px;
    }

    /* Make template name (kali) black */
    .template-card .card-header .template-title {
        color: #000000 !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="jumbotron jumbotron-fluid bg-primary text-white">
    <div class="container">
        <h1><i class="fas fa-desktop mr-2"></i> Web Desktop</h1>
        <p>Launch a desktop environment for security testing and CTF challenges.</p>
    </div>
</div>

<div class="container">
    <div id="alert-container"></div>

    <!-- Container Information -->
    <div id="container-status" class="desktop-container">
        {% if container %}
        <div id="container-info" class="row justify-content-center">
            <div class="col-lg-8">
                <div class="desktop-controls card-neu">
                    <div class="desktop-header bg-primary text-white p-3 rounded-top">
                        <img src="{{ url_for('plugins.web_desktop.assets', path='img/' + template_info.icon) if template_info else url_for('plugins.web_desktop.assets', path='img/desktop-icon.svg') }}"
                             alt="{% if template_info %}{{ template_info.name }}{% else %}Desktop Environment{% endif %}" class="desktop-header-icon">
                        <div>
                            <h3>{% if template_info %}{{ template_info.name }}{% elif container.challenge %}{{ container.challenge.name }}{% else %}Desktop Environment{% endif %}</h3>
                            <span id="status-badge" class="status-badge status-running" style="background-color: #28a745; border: none; box-shadow: none;">Running</span>
                        </div>
                    </div>

                    <div class="desktop-info-card wd-neu-flat">
                        <div class="desktop-info-item">
                            <i class="fas fa-clock" style="color: #00c8ff;"></i>
                            <div>
                                <span class="info-label" style="color: #00c8ff; font-weight: bold;">Started</span>
                                <span class="info-value" style="color: #ffffff;">{{ container.start_time.strftime('%Y-%m-%d %H:%M:%S') }}</span>
                            </div>
                        </div>

                        <div class="desktop-info-item">
                            <i class="fas fa-hourglass-half" style="color: #ffcc00;"></i>
                            <div>
                                <span class="info-label" style="color: #ffcc00; font-weight: bold;">Time Remaining</span>
                                <span class="info-value" id="time-remaining" style="color: #ffffff;">
                                    {% set timeout = config.get('web_desktop:docker_timeout', 3600)|int %}
                                    {% set elapsed_seconds = (now - container.start_time).total_seconds()|int %}
                                    {% set remaining_seconds = timeout - elapsed_seconds %}
                                    {% if remaining_seconds > 0 %}
                                        {% set hours = (remaining_seconds // 3600)|int %}
                                        {% set minutes = ((remaining_seconds % 3600) // 60)|int %}
                                        {% set seconds = (remaining_seconds % 60)|int %}
                                        {% if hours > 0 %}
                                            {{ hours }}h {{ minutes }}m {{ seconds }}s
                                        {% else %}
                                            {{ minutes }}m {{ seconds }}s
                                        {% endif %}
                                    {% else %}
                                        Expiring soon
                                    {% endif %}
                                </span>
                            </div>
                        </div>

                        <div class="desktop-info-item">
                            <i class="fas fa-sync" style="color: #00ff9d;"></i>
                            <div>
                                <span class="info-label" style="color: #00ff9d; font-weight: bold;">Renewals</span>
                                <span class="info-value" style="color: #ffffff;">{{ container.renew_count }} / {{ config.get('web_desktop:docker_max_renew_count', 5) }}</span>
                            </div>
                        </div>

                        <!-- Debug: Template info: {{ template_info }} -->
                        <!-- Debug: Docker image: {{ template_info.docker_image if template_info else 'None' }} -->
                        {% if template_info and (template_info.docker_image == "my-kali-desktop:1.14.0" or 'kali' in template_info.docker_image|lower) %}
                        <div class="desktop-info-item desktop-credentials wd-neu-flat" style="background-color: rgba(0, 0, 0, 0.3); border: 1px solid rgba(255, 255, 255, 0.15); padding: 12px; border-radius: 8px;">
                            <i class="fas fa-key" style="color: #ff5f5f;"></i>
                            <div>
                                <span class="info-label" style="color: #ff5f5f; font-weight: bold;">Login Credentials</span>
                                <span class="info-value" style="color: #ffffff;">
                                    Username: <strong style="color: #00ffcc; background-color: rgba(0, 0, 0, 0.3); padding: 2px 6px; border-radius: 4px;">kasm_user</strong> /
                                    Password: <strong style="color: #00ffcc; background-color: rgba(0, 0, 0, 0.3); padding: 2px 6px; border-radius: 4px;">password</strong>
                                </span>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    {% if template_info and (template_info.docker_image == "my-kali-desktop:1.14.0" or 'kali' in template_info.docker_image|lower) %}
                    <div class="desktop-info-card wd-neu-flat mt-3 challenge-info-card">
                        <div class="card-header text-white" style="background-color: #00366e; border-bottom: 2px solid #00a8ff; padding: 12px;">
                            <i class="fas fa-info-circle mr-2" style="color: #00ffff;"></i> <span style="color: #00ffff; font-weight: bold; font-size: 1.1em;">Challenge Container Access</span>
                        </div>
                        <div class="card-body">
                            <p style="color: #6fc2ff !important; font-weight: 500; font-size: 1.05em;" class="access-instructions">To access challenge containers from this desktop, use the following URL format:</p>
                            <div class="alert" style="background-color: rgba(0, 0, 0, 0.3); border: 1px solid #6fc2ff; border-radius: 8px;">
                                <code style="background-color: rgba(0, 0, 0, 0.4); color: #00ffcc; padding: 6px 12px; border-radius: 4px; font-size: 1.1em;">http://challenge:[port]</code>
                            </div>

                            {% if challenge_container and challenge_container.port %}
                            <div class="alert" style="background-color: #003b00; border: 1px solid #00ff44; color: #00ff44; border-radius: 8px;">
                                <i class="fas fa-check-circle mr-2" style="color: #00ff44;"></i> <span style="font-weight: bold; color: #00ff44;">You have an active challenge container!</span>
                                <div class="mt-2" style="color: #ffffff;">
                                    <strong style="color: #00ffcc;">Access URL:</strong> <code style="background-color: rgba(0, 0, 0, 0.3); color: #00ffcc; padding: 4px 8px; border-radius: 4px;">http://challenge:{% if challenge_container.internal_port %}{{ challenge_container.internal_port }}{% else %}{{ challenge_container.port }}{% endif %}</code>
                                </div>
                            </div>
                            {% else %}
                            <div class="alert" style="background-color: #2d2000; border: 1px solid #ffcc00; color: #ffcc00; border-radius: 8px;">
                                <i class="fas fa-exclamation-triangle mr-2" style="color: #ffcc00;"></i> <span style="font-weight: bold; color: #ffcc00;">No active challenge containers found.</span>
                                <div class="mt-2" style="color: #ffffff;">Launch a challenge from the Challenges page to get started.</div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <div class="desktop-actions">
                        <a href="https://{{ config.get('web_desktop:domain', 'localhost') }}:{{ container.port }}"
                        id="direct-launch-btn" class="btn btn-lg btn-success btn-block direct-launch-btn mb-3 wd-btn-transform btn-pulse"
                        data-url="https://{{ config.get('web_desktop:domain', 'localhost') }}:{{ container.port }}"
                        target="_blank" style="font-size: 1.2rem; padding: 15px; background: none; background-color: #ffffff; color: #007bff; border: 1px solid #007bff;">
                            <i class="fas fa-desktop mr-2"></i> Access Your Desktop
                        </a>

                        <div class="action-buttons-row">
                            <form method="POST" action="{{ url_for('web_desktop.renew_desktop') }}" class="action-form">
                                <input type="hidden" name="nonce" value="{{ nonce }}">
                                <button type="submit" class="btn btn-success"
                                    {% if container.renew_count >= config.get('web_desktop:docker_max_renew_count', 5)|int %}disabled{% endif %}>
                                    <i class="fas fa-clock mr-2"></i> Renew
                                </button>
                            </form>

                            <form method="POST" action="{{ url_for('web_desktop.destroy_desktop') }}" class="action-form"
                                  onsubmit="return confirm('Are you sure you want to destroy this desktop? All unsaved data will be lost.')">
                                <input type="hidden" name="nonce" value="{{ nonce }}">
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash-alt mr-2"></i> Destroy
                                </button>
                            </form>
                        </div>
                    </div>


                </div>
            </div>
        </div>
        {% else %}
        <div id="template-selection">
            <div class="template-header text-center mb-4 wd-fade-in" style="background-color: #1a2b47; border-radius: 12px; padding: 25px; box-shadow: none;">
                <h2 class="text-white"><i class="fas fa-th-large mr-2"></i> Select a Desktop Environment</h2>
                <p class="lead text-light">Choose from the available templates below to launch your desktop</p>
            </div>

            <div class="row template-grid wd-fade-in">

            {% for template in templates %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card template-card h-100 {% if template.recommended %}border-primary{% endif %}" style="background-color: var(--wd-card-bg, #212529);">
                    <div class="card-header d-flex justify-content-between align-items-center {% if template.recommended %}bg-primary text-white{% endif %}">
                        <div class="template-title">
                            <img src="{{ url_for('plugins.web_desktop.assets', path='img/' + template.icon) }}"
                                 alt="{{ template.name }}" width="24" height="24" class="mr-2"
                                 onerror="this.onerror=null;this.src='{{ url_for('plugins.web_desktop.assets', path='img/desktop-icon.svg') }}';">
                            {{ template.name }}
                        </div>
                        {% if template.recommended %}
                        <span class="badge badge-light" style="background-color: #ffffff; color: #007bff; font-weight: bold; padding: 5px 10px; box-shadow: none; text-shadow: none;">Recommended</span>
                        {% endif %}
                    </div>
                    <div class="card-body d-flex flex-column">
                        <div class="text-center mb-3">
                            <img src="{{ url_for('plugins.web_desktop.assets', path='img/' + template.icon) }}"
                                 alt="{{ template.name }}" class="template-icon"
                                 onerror="this.onerror=null;this.src='{{ url_for('plugins.web_desktop.assets', path='img/desktop-icon.svg') }}';">
                        </div>
                        {% if template.description %}
                        <p class="template-description">{{ template.description }}</p>
                        {% endif %}
                        <div class="template-specs mt-auto">
                            <div class="spec-item">
                                <i class="fas fa-microchip text-muted"></i>
                                <span style="color: #e2e5e9;"><strong style="color: #6fc2ff;">CPU:</strong> {{ template.cpu_limit }}</span>
                            </div>
                            <div class="spec-item">
                                <i class="fas fa-memory text-muted"></i>
                                <span style="color: #e2e5e9;"><strong style="color: #6fc2ff;">Memory:</strong> {{ template.memory_limit }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer text-center">
                        <form method="POST" action="{{ url_for('web_desktop.launch_desktop', template_id=template.id) }}">
                            <input type="hidden" name="nonce" value="{{ nonce }}">
                            <button type="submit" class="btn btn-lg btn-primary btn-block launch-btn wd-btn-transform" style="background: none; background-color: #ffffff; color: #007bff; font-weight: 600; letter-spacing: 0.5px; border: 1px solid #007bff;" onclick="this.disabled=true; this.innerHTML='<i class=\'fas fa-spinner fa-spin mr-2\'></i> Launching...';">
                                <i class="fas fa-rocket mr-2"></i> Launch
                            </button>
                            <script>
                                // Ensure the button has a click handler
                                document.addEventListener('DOMContentLoaded', function() {
                                    const launchBtn = document.querySelector('.launch-btn');
                                    if (launchBtn) {
                                        console.log('Found launch button, adding click handler');
                                        launchBtn.addEventListener('click', function() {
                                            console.log('Launch button clicked');
                                            this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Launching...';
                                            this.disabled = true;
                                        });
                                    }
                                });
                            </script>
                        </form>
                    </div>
                </div>
            </div>
            {% endfor %}

            {% if templates|length == 0 %}
            <div class="col-12">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle mr-2"></i> No desktop templates are available.
                </div>
            </div>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<div id="loading-overlay" class="loading-overlay" style="display: none !important; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.9); z-index: 9999; justify-content: center; align-items: center; flex-direction: column;">
    <script>document.getElementById('loading-overlay').style.display = 'none';</script>
    <div class="wd-challenge-spinner" style="display: inline-block; width: 100px; height: 100px; border: 6px solid rgba(255, 255, 255, 0.2); border-radius: 50%; border-top-color: #007bff; animation: challenge-spin 1s ease-in-out infinite; margin-bottom: 25px;"></div>
    <div id="loading-text" class="wd-challenge-text" style="color: #fff; font-size: 24px; margin-bottom: 25px; font-weight: 600;">Preparing your desktop environment...</div>
    <div id="loading-status" class="wd-challenge-status" style="color: #6fc2ff; font-size: 18px; margin-bottom: 25px;">Initializing...</div>

    <!-- New simple progress bar -->
    <div class="progress-container" style="width: 350px; height: 30px; background-color: #222; border-radius: 15px; padding: 5px; margin: 0 auto; box-shadow: 0 0 10px rgba(0,0,0,0.5);">
        <div id="simple-progress-bar" style="width: 0%; height: 100%; background-color: #28a745; border-radius: 10px; transition: none; position: relative;">
            <span id="progress-percentage" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-weight: bold; text-shadow: 1px 1px 2px rgba(0,0,0,0.7);">0%</span>
        </div>
    </div>

    <!-- Debug information (only visible to admins) -->
    {% if is_admin() %}
    <div id="debug-info" style="margin-top: 30px; background-color: rgba(0, 0, 0, 0.7); padding: 15px; border-radius: 5px; max-width: 80%; max-height: 200px; overflow-y: auto; text-align: left;">
        <h5 style="color: #ff9800; margin-bottom: 10px;">Debug Information (Admin Only)</h5>
        <div id="debug-log" style="color: #aaa; font-family: monospace; font-size: 12px;">Waiting for debug information...</div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<!-- Simple loading script for maximum reliability -->
<script src="{{ url_for('plugins.web_desktop.assets', path='js/simple-loader.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Make sure loading overlay is hidden on page load
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.setAttribute('style', 'display: none !important; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.9); z-index: 9999; justify-content: center; align-items: center; flex-direction: column;');
        console.log('Hiding loading overlay on page load');
    }

    // Apply animations to elements on load
    animateElementsOnLoad();

    // Initialize direct launch button
    const directLaunchBtn = document.getElementById('direct-launch-btn');
    if (directLaunchBtn) {
        directLaunchBtn.addEventListener('click', function(e) {
            // Show loading overlay when opening in new tab
            showLoading('Opening desktop in new tab...');
            setTimeout(hideLoading, 1500); // Hide after 1.5 seconds
        });
    }

    // Form submission is now handled by guaranteed-loader.js
    // This code is disabled to avoid conflicts
    console.log('Using guaranteed-loader.js for form submission with 15-second delay');

    // Update time remaining counter with improved formatting including seconds
    const timeRemainingElement = document.getElementById('time-remaining');
    if (timeRemainingElement) {
        console.log('Initial time display:', timeRemainingElement.textContent);

        // Check if the container is already expired
        if (timeRemainingElement.textContent.trim() === 'Expiring soon') {
            console.log('Container is already expired or close to expiring');
        } else {
            // Update the time display immediately
            updateRemainingTime(timeRemainingElement);

            // Update every second instead of every minute
            setInterval(() => updateRemainingTime(timeRemainingElement), 1000);
        }
    } else {
        console.log('Time remaining element not found');
    }
});

// Helper functions
function animateElementsOnLoad() {
    // Function now empty as animations are removed
    // No animations to add
}

function updateRemainingTime(element) {
    let text = element.textContent.trim();
    let totalSeconds = 0;

    // Check if the text is 'Expiring soon'
    if (text === 'Expiring soon') {
        // Already expiring, no need to update
        element.style.color = '#ff4d4d'; // Bright red
        element.parentElement.querySelector('.info-label').style.color = '#ff4d4d';
        return;
    }

    // Parse the current time display
    if (text.includes('minutes')) {
        // Format is like "59 minutes"
        const minutes = parseInt(text);
        totalSeconds = minutes * 60;
    } else if (text.includes('h') && text.includes('m') && text.includes('s')) {
        // Format is like "1h 30m 45s"
        const parts = text.split(' ');
        let hours = 0, minutes = 0, seconds = 0;

        for (const part of parts) {
            if (part.includes('h')) {
                hours = parseInt(part);
            } else if (part.includes('m')) {
                minutes = parseInt(part);
            } else if (part.includes('s')) {
                seconds = parseInt(part);
            }
        }

        totalSeconds = (hours * 3600) + (minutes * 60) + seconds;
    } else if (text.includes('m') && text.includes('s')) {
        // Format is like "30m 45s"
        const parts = text.split(' ');
        let minutes = 0, seconds = 0;

        for (const part of parts) {
            if (part.includes('m')) {
                minutes = parseInt(part);
            } else if (part.includes('s')) {
                seconds = parseInt(part);
            }
        }

        totalSeconds = (minutes * 60) + seconds;
    }

    // Decrement by 1 second
    totalSeconds = totalSeconds - 1;

    if (isNaN(totalSeconds) || totalSeconds <= 0) {
        element.textContent = 'Expiring soon';
        element.style.color = '#ff4d4d'; // Bright red
        element.parentElement.querySelector('.info-label').style.color = '#ff4d4d';
    } else {
        // Calculate hours, minutes, seconds
        const hours = Math.floor(totalSeconds / 3600);
        const remainingMinutes = Math.floor((totalSeconds % 3600) / 60);
        const remainingSeconds = totalSeconds % 60;

        // Format with hours, minutes, and seconds
        if (hours > 0) {
            element.textContent = `${hours}h ${remainingMinutes}m ${remainingSeconds}s`;
            element.style.color = '#ffffff'; // White
        } else {
            element.textContent = `${remainingMinutes}m ${remainingSeconds}s`;

            // Change color based on remaining time
            if (remainingMinutes < 5) {
                // Less than 5 minutes - red
                element.style.color = '#ff4d4d';
                element.parentElement.querySelector('.info-label').style.color = '#ff4d4d';
            } else if (remainingMinutes < 10) {
                // Less than 10 minutes - orange
                element.style.color = '#ffaa00';
                element.parentElement.querySelector('.info-label').style.color = '#ffaa00';
            } else {
                // More than 10 minutes - white
                element.style.color = '#ffffff';
            }
        }

        // Log the time for debugging
        console.log(`Time remaining: ${element.textContent}`);
    }
}

function showLoading(message) {
    // This function is now simplified to avoid conflicts with guaranteed-loader.js
    // It's only used for the direct launch button, not for form submissions
    const loadingOverlay = document.getElementById('loading-overlay');
    const loadingText = document.getElementById('loading-text');
    const progressBar = document.getElementById('loading-progress-bar');

    if (loadingOverlay) {
        // Force display style to override the !important
        loadingOverlay.setAttribute('style', 'display: flex !important; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.9); z-index: 9999; justify-content: center; align-items: center; flex-direction: column;');

        // Set the loading message
        if (loadingText && message) {
            loadingText.textContent = message;
        }

        // Reset progress bar
        if (progressBar) {
            progressBar.style.width = '20%'; // Start at 20% to make it more visible immediately
        }

        console.log('Loading overlay displayed with message:', message || 'No message provided');
    } else {
        console.error('Loading overlay not found');
    }
}

function hideLoading() {
    // If we're in the middle of loading a desktop, don't hide the overlay
    if (window.isLoadingDesktop) {
        console.log('Loading desktop in progress, not hiding overlay');
        return;
    }

    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        // Fade out effect
        loadingOverlay.style.opacity = '0';
        setTimeout(() => {
            // Force display style to override the !important
            loadingOverlay.setAttribute('style', 'display: none !important; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.9); z-index: 9999; justify-content: center; align-items: center; flex-direction: column; opacity: 1;');
        }, 300);
    }
}

function showMessage(message, type) {
    const alertContainer = document.getElementById('alert-container');
    if (!alertContainer) return;

    // Remove any existing alerts
    const existingAlerts = alertContainer.querySelectorAll('.alert');
    existingAlerts.forEach(alert => {
        if (alertContainer.contains(alert)) {
            alertContainer.removeChild(alert);
        }
    });

    // Create the new alert with animation
    const alertElement = document.createElement('div');
    alertElement.className = `alert alert-${type} alert-dismissible fade show wd-slide-in-right`;
    alertElement.setAttribute('role', 'alert');
    alertElement.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    `;

    alertContainer.appendChild(alertElement);

    // Auto-dismiss after 5 seconds
    setTimeout(function() {
        try {
            alertElement.classList.add('wd-slide-in-left');
            alertElement.classList.remove('show');
            setTimeout(() => {
                if (alertContainer.contains(alertElement)) {
                    alertContainer.removeChild(alertElement);
                }
            }, 500);
        } catch (error) {
            console.error('Error removing alert:', error);
        }
    }, 5000);
}
</script>

<!-- Direct inline script to ensure loading animation works -->
<script>
console.log('Direct inline script loaded');

// Find all launch forms
const directLaunchForms = document.querySelectorAll('form[action*="launch_desktop"]');
console.log('Found ' + directLaunchForms.length + ' launch forms (direct)');

// Add event listeners to all forms
directLaunchForms.forEach(form => {
    console.log('Adding submit handler to form (direct)');
    form.addEventListener('submit', function(e) {
        console.log('Form submission intercepted (direct)');
        e.preventDefault();

        // Disable all buttons
        const buttons = document.querySelectorAll('button');
        buttons.forEach(btn => {
            btn.disabled = true;
            if (btn.classList.contains('launch-btn')) {
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Launching...';
            }
        });

        // Create loading overlay
        const overlay = document.createElement('div');
        overlay.style.position = 'fixed';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.width = '100%';
        overlay.style.height = '100%';
        overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
        overlay.style.zIndex = '9999';
        overlay.style.display = 'flex';
        overlay.style.flexDirection = 'column';
        overlay.style.justifyContent = 'center';
        overlay.style.alignItems = 'center';
        document.body.appendChild(overlay);

        // Create spinner
        const spinner = document.createElement('div');
        spinner.style.width = '100px';
        spinner.style.height = '100px';
        spinner.style.border = '8px solid #444';
        spinner.style.borderTop = '8px solid #007bff';
        spinner.style.borderRadius = '50%';
        spinner.style.marginBottom = '30px';
        spinner.style.animation = 'spin 1s linear infinite';
        overlay.appendChild(spinner);

        // Create text
        const text = document.createElement('div');
        text.style.color = 'white';
        text.style.fontSize = '24px';
        text.style.fontWeight = 'bold';
        text.style.marginBottom = '20px';
        text.textContent = 'Preparing your desktop environment...';
        overlay.appendChild(text);

        // Create status text
        const status = document.createElement('div');
        status.style.color = '#6fc2ff';
        status.style.fontSize = '18px';
        status.style.marginBottom = '30px';
        status.textContent = 'Initializing...';
        overlay.appendChild(status);

        // Create progress container
        const progressContainer = document.createElement('div');
        progressContainer.style.width = '400px';
        progressContainer.style.height = '10px';
        progressContainer.style.backgroundColor = '#333';
        progressContainer.style.borderRadius = '5px';
        progressContainer.style.overflow = 'hidden';
        progressContainer.style.marginBottom = '20px';
        overlay.appendChild(progressContainer);

        // Create progress bar
        const progressBar = document.createElement('div');
        progressBar.style.width = '0%';
        progressBar.style.height = '100%';
        progressBar.style.backgroundColor = '#007bff';
        progressContainer.appendChild(progressBar);

        // Create countdown
        const countdown = document.createElement('div');
        countdown.style.color = '#aaa';
        countdown.style.fontSize = '16px';
        countdown.textContent = 'Time remaining: 30 seconds';
        overlay.appendChild(countdown);

        // Status messages
        const messages = [
            'Initializing...',
            'Creating container...',
            'Allocating resources...',
            'Setting up environment...',
            'Installing components...',
            'Configuring network...',
            'Starting desktop services...',
            'Preparing user interface...',
            'Almost ready...',
            'Finalizing setup...'
        ];

        // Start time
        const startTime = Date.now();
        const duration = 30000; // 30 seconds

        // Update progress and status
        const interval = setInterval(() => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(100, (elapsed / duration) * 100);
            const timeLeft = Math.max(0, Math.ceil((duration - elapsed) / 1000));

            // Update progress bar
            progressBar.style.width = progress + '%';

            // Update countdown
            countdown.textContent = `Time remaining: ${timeLeft} seconds`;

            // Update status message
            const messageIndex = Math.min(Math.floor(progress / 10), messages.length - 1);
            status.textContent = messages[messageIndex];

            // Check if complete
            if (progress >= 100) {
                clearInterval(interval);
            }
        }, 100);

        // Submit form after delay - with more robust handling
        setTimeout(() => {
            console.log('Now submitting form after delay (direct)');
            try {
                // Create a completely new form with the same action and method
                const newForm = document.createElement('form');
                newForm.method = form.method;
                newForm.action = form.action;

                // Copy all form fields
                const formData = new FormData(form);
                formData.forEach((value, key) => {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = key;
                    input.value = value;
                    newForm.appendChild(input);
                });

                // Log what we're about to submit
                console.log('Submitting to:', newForm.action);
                console.log('With method:', newForm.method);

                // Add the form to the document and submit it
                document.body.appendChild(newForm);
                newForm.submit();

                // Also try the original form as a fallback
                setTimeout(() => {
                    console.log('Fallback: submitting original form');
                    form.submit();
                }, 500);
            } catch (error) {
                console.error('Error during form submission:', error);
                // Last resort - redirect to the form action URL
                window.location.href = form.action;

                // Ultimate fallback - reload the page after 2 more seconds
                setTimeout(() => {
                    console.log('Ultimate fallback: reloading page');
                    window.location.reload();
                }, 2000);
            }
        }, duration);
    });
});
</script>
{% endblock %}